services:
  # Drupal 7 (main application)
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - db
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html
    container_name: prox_drupal7

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: Ni<PERSON>la@123
      MYSQL_DATABASE: prox_local
      MYSQL_USER: prox_user
      MYSQL_PASSWORD: prox_pass
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./db:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    container_name: prox_mysql

volumes:
  mysql_data:
