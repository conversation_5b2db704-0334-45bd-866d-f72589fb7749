#!/bin/bash

# ProX Local Development Environment Manager

echo "🚀 ProX Local Development Environment"
echo "======================================"

case "$1" in
    start)
        echo "Starting containers..."
        docker-compose up -d
        echo "✅ Containers started!"
        echo ""
        echo "🌐 Drupal 7: http://localhost:8080"
        echo "🗄️  Database: localhost:3307 (user: root, password: <PERSON><PERSON><PERSON>@123)"
        ;;
    stop)
        echo "Stopping containers..."
        docker-compose down
        echo "✅ Containers stopped!"
        ;;
    restart)
        echo "Restarting containers..."
        docker-compose down
        docker-compose up -d
        echo "✅ Containers restarted!"
        echo ""
        echo "🌐 Drupal 7: http://localhost:8080"
        ;;
    rebuild)
        echo "Rebuilding and starting containers..."
        docker-compose down
        docker-compose up --build -d
        echo "✅ Containers rebuilt and started!"
        echo ""
        echo "🌐 Drupal 7: http://localhost:8080"
        ;;

    logs)
        if [ -n "$2" ]; then
            echo "Showing logs for $2..."
            docker-compose logs -f "$2"
        else
            echo "Showing logs for all containers..."
            docker-compose logs -f
        fi
        ;;
    status)
        echo "Container status:"
        docker-compose ps
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|rebuild|logs|status}"
        echo ""
        echo "Commands:"
        echo "  start     - Start the development environment"
        echo "  stop      - Stop the development environment"
        echo "  restart   - Restart the development environment"
        echo "  rebuild   - Rebuild and start the development environment"
        echo "  logs      - Show container logs (optional: specify container name)"
        echo "  status    - Show container status"
        echo ""
        echo "Examples:"
        echo "  $0 logs web       - Show Drupal 7 logs"
        echo "  $0 logs db        - Show database logs"
        exit 1
        ;;
esac
